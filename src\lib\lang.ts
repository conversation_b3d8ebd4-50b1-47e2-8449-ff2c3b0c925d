import { useState, useCallback, useEffect } from 'react';
import { useTolgee } from '@tolgee/react';

// Direct translations (embedded for testing)
const DIRECT_TRANSLATIONS: Record<string, Record<string, string>> = {
  en: {
    "startup_message": "Starting Chatbot in English.",
    "target_prompt": "What is your target language?",
    "available_options": "Available options",
    "no_matches": "No matches found. Using AI translation...",
    "ai_translation": "AI Proposed The Translation",
    "choose_valid_option": "Please choose a valid option number between",
    "match_selected": "Match selected",
    "translating": "Translating...",
    "no_translation_found": "No translations were found",
    "error_try_again": "There was an error querying fuzzy matches. Please try again!",
    "ask_new_word": "What medical term would you like to look up",
    "ask_new_word_again": "What else would you like to translate",
    "change_lang_prompt": "If you would like to change the source or target language, type \"/source\" or \"/target\".",
    "source_prompt": "What is your source language?",
    "conversation_complete": "The conversation is complete. Please refresh to start a new translation.",
    "invalid_language": "I didn't find that language."
  },
  ru: {
    "startup_message": "Запуск чат-бота на русском языке.",
    "target_prompt": "Какой ваш целевой язык?",
    "available_options": "Доступные варианты",
    "no_matches": "Совпадений не найдено. Использую перевод ИИ...",
    "ai_translation": "ИИ предложил перевод",
    "choose_valid_option": "Пожалуйста, выберите действительный номер варианта между",
    "match_selected": "Совпадение выбрано",
    "translating": "Перевод...",
    "no_translation_found": "Переводы не найдены",
    "error_try_again": "Произошла ошибка при запросе нечетких совпадений. Попробуйте еще раз!",
    "ask_new_word": "Какой медицинский термин вы хотите найти",
    "ask_new_word_again": "Что еще вы хотите перевести",
    "change_lang_prompt": "Если вы хотите изменить исходный или целевой язык, введите \"/source\" или \"/target\".",
    "source_prompt": "Яка ваша вихідна мова?",
    "conversation_complete": "Розмова завершена. Оновіть сторінку, щоб розпочати новий переклад.",
    "invalid_language": "Я не знайшов цю мову."
  },
  uk: {
    "startup_message": "Запуск чат-бота українською мовою.",
    "target_prompt": "Яка ваша цільова мова?",
    "available_options": "Доступні варіанти",
    "no_matches": "Збігів не знайдено. Використовую переклад ШІ...",
    "ai_translation": "ШІ запропонував переклад",
    "choose_valid_option": "Будь ласка, виберіть дійсний номер варіанта між",
    "match_selected": "Збіг вибрано",
    "translating": "Переклад...",
    "no_translation_found": "Переклади не знайдено",
    "error_try_again": "Сталася помилка під час запиту нечітких збігів. Спробуйте ще раз!",
    "ask_new_word": "Який медичний термін ви хочете знайти",
    "ask_new_word_again": "Що ще ви хочете перекласти",
    "change_lang_prompt": "Якщо ви хочете змінити вихідну або цільову мову, введіть \"/source\" або \"/target\".",
    "source_prompt": "Яка ваша вихідна мова?",
    "conversation_complete": "Розмова завершена. Оновіть сторінку, щоб розпочати новий переклад.",
    "invalid_language": "Я не знайшов цю мову."
  }
};

// Keep existing UI_STRINGS for fallback
export const UI_STRINGS: Record<string, Record<string, string>> = {
    en: {
        startup_message: 'Starting Chatbot in English.',
        start_message: 'What is your target language?',
        available_options: 'Available options',
        no_matches: 'No matches found. Using AI translation...',
        ai_translation: 'AI Proposed The Translation',
        choose_valid_option: 'Please choose a valid option number between',
        match_selected: 'Match selected',
        translating: 'Translating...',
        no_translation_found: 'No translations were found',
        error_try_again: 'There was an error querying fuzzy matches. Please try again!',
        ask_new_word: 'What medical term would you like to look up',
        ask_new_word_again: 'What else would you like to translate',
        change_lang_prompt: 'If you would like to change the source or target language, type "/source" or "/target".',
        source_prompt: 'What is your source language?',
        target_prompt: 'What is your target language?',
        conversation_complete: 'The conversation is complete. Please refresh to start a new translation.',
        invalid_language: 'I didn’t find that language.',
    },      
    ru: {
        startup_message: 'Starting Chatbot in Russian.',
        start_message: 'What is your target language? (Russian Translation Needed)',
        available_options: 'Available options (Russian Translation Needed)',
        no_matches: 'No matches found. Using AI translation... (Russian Translation Needed)',
        ai_translation: 'AI Proposed The Translation (Russian Translation Needed)',
        choose_valid_option: 'Please choose a valid option number between (Russian Translation Needed)',
        match_selected: 'Match selected (Russian Translation Needed)',
        translating: 'Translating... (Russian Translation Needed)',
        no_translation_found: 'No translations were found (Russian Translation Needed)',
        error_try_again: 'There was an error querying fuzzy matches. Please try again! (Russian Translation Needed)',
        ask_new_word: 'What medical term would you like to look up (Russian Translation Needed)',
        ask_new_word_again: 'What else would you like to translate (Russian Translation Needed)',
        change_lang_prompt: 'If you would like to change the source or target language, type "/source" or "/target". (Russian Translation Needed)',
        source_prompt: 'What is your source language? (Russian Translation Needed)',
        target_prompt: 'What is your target language? (Russian Translation Needed)',
        conversation_complete: 'The conversation is complete. Please refresh to start a new translation. (Russian Translation Needed)',
        invalid_language: 'I didn’t find that language. (Russian Translation Needed)',
    },
    uk: {
        startup_message: 'Starting Chatbot in Ukranian.',
        start_message: 'What is your target language? (Ukranian Translation Needed)',
        available_options: 'Available options (Ukranian Translation Needed)',
        no_matches: 'No matches found. Using AI translation... (Ukranian Translation Needed)',
        ai_translation: 'AI Proposed The Translation (Ukranian Translation Needed)',
        choose_valid_option: 'Please choose a valid option number between (Ukranian Translation Needed)',
        match_selected: 'Match selected (Ukranian Translation Needed)',
        translating: 'Translating... (Ukranian Translation Needed)',
        no_translation_found: 'No translations were found (Ukranian Translation Needed)',
        error_try_again: 'There was an error querying fuzzy matches. Please try again! (Ukranian Translation Needed)',
        ask_new_word: 'What medical term would you like to look up (Ukranian Translation Needed)',
        ask_new_word_again: 'What else would you like to translate (Ukranian Translation Needed)',
        change_lang_prompt: 'If you would like to change the source or target language, type "/source" or "/target". (Ukranian Translation Needed)',
        source_prompt: 'What is your source language? (Ukranian Translation Needed)',
        target_prompt: 'What is your target language? (Ukranian Translation Needed)',
        conversation_complete: 'The conversation is complete. Please refresh to start a new translation. (Ukranian Translation Needed)',
        invalid_language: 'I didn’t find that language. (Ukranian Translation Needed)',
    },
  };
  
  // Updated function that uses direct translations first, then falls back to UI_STRINGS
  export function getUIString(key: string, lang: string): string {
    // Use direct translations first
    return DIRECT_TRANSLATIONS[lang]?.[key] || DIRECT_TRANSLATIONS['en'][key] || UI_STRINGS[lang]?.[key] || UI_STRINGS['en'][key] || key;
  }

  // Enhanced translation hook that uses Tolgee with proper fallbacks
  export function useTranslation() {
    const [currentLanguage, setCurrentLanguage] = useState('en');

    // Always call useTolgee to follow React Hook rules
    let tolgee = null;
    let tolgeeAvailable = false;

    try {
      tolgee = useTolgee(['language']);
      tolgeeAvailable = true;
    } catch (error) {
      console.warn('Tolgee provider not available, using fallback system');
      tolgeeAvailable = false;
    }

    // Sync current language with Tolgee when it changes
    useEffect(() => {
      if (tolgee && tolgeeAvailable) {
        const tolgeeLanguage = tolgee.getLanguage();
        if (tolgeeLanguage && tolgeeLanguage !== currentLanguage) {
          setCurrentLanguage(tolgeeLanguage);
        }
      }
    }, [tolgee, tolgeeAvailable, currentLanguage]);

    const t = useCallback((key: string, params?: Record<string, string | number>) => {
      // Try Tolgee first if available
      if (tolgee && tolgeeAvailable) {
        try {
          const translation = tolgee.t(key, params);
          const tolgeeCurrentLang = tolgee.getLanguage();

          // Enhanced debug: Log what Tolgee is returning for ALL keys
          console.log(`🔍 Tolgee Debug - Current Lang: ${currentLanguage}, Tolgee Lang: ${tolgeeCurrentLang}, Key: "${key}", Translation: "${translation}"`);

          // Check if we got a real translation (not just the key back)
          if (translation && translation !== key) {
            console.log(`✅ Tolgee SUCCESS for "${key}": "${translation}"`);
            return translation;
          } else {
            console.warn(`⚠️ Tolgee returned the key itself for "${key}" in "${currentLanguage}" (Tolgee says: "${tolgeeCurrentLang}") - using fallback`);
          }
        } catch (error) {
          console.warn('Tolgee translation failed for key:', key, error);
        }
      } else {
        console.log(`🚫 Tolgee not available for key "${key}"`);
      }

      // Fallback to local translations
      const fallbackResult = getUIString(key, currentLanguage);
      console.log(`📝 Using fallback translation - Language: ${currentLanguage}, Key: ${key}, Result: "${fallbackResult}"`);
      return fallbackResult;
    }, [tolgee, tolgeeAvailable, currentLanguage]);

    const changeLanguage = useCallback(async (lang: string) => {
      console.log('Changing language to:', lang);

      // Try to update Tolgee language first if available
      if (tolgee && tolgeeAvailable) {
        try {
          await tolgee.changeLanguage(lang);
          console.log('Tolgee language changed to:', lang);

          // Wait a bit for Tolgee to update, then sync our state
          setTimeout(() => {
            const actualTolgeeLanguage = tolgee.getLanguage();
            console.log('Tolgee actual language after change:', actualTolgeeLanguage);
            setCurrentLanguage(actualTolgeeLanguage || lang);

            // Force a re-render by triggering a state change
            setCurrentLanguage(prev => {
              console.log('Force refresh: prev =', prev, ', new =', actualTolgeeLanguage || lang);
              return actualTolgeeLanguage || lang;
            });
          }, 200);
        } catch (error) {
          console.warn('Failed to change Tolgee language:', error);
          // Fallback to just updating our state
          setCurrentLanguage(lang);
        }
      } else {
        console.log('Using fallback language change to:', lang);
        setCurrentLanguage(lang);
      }
    }, [tolgee, tolgeeAvailable]);

    // Sync with Tolgee language when it becomes available
    useEffect(() => {
      if (tolgee && tolgeeAvailable) {
        const tolgeeLanguage = tolgee.getLanguage();
        console.log('Syncing with Tolgee language:', tolgeeLanguage);
        if (tolgeeLanguage && tolgeeLanguage !== currentLanguage) {
          setCurrentLanguage(tolgeeLanguage);
        }

        // Set up a periodic sync to catch Tolgee changes
        const syncInterval = setInterval(() => {
          const currentTolgeeLanguage = tolgee.getLanguage();
          if (currentTolgeeLanguage && currentTolgeeLanguage !== currentLanguage) {
            console.log('Periodic sync: Tolgee language changed to:', currentTolgeeLanguage);
            setCurrentLanguage(currentTolgeeLanguage);
          }
        }, 500);

        return () => clearInterval(syncInterval);
      }
    }, [tolgee, tolgeeAvailable, currentLanguage]);

    const getCurrentLanguage = useCallback(() => {
      return currentLanguage;
    }, [currentLanguage]);

    return {
      t,
      changeLanguage,
      getCurrentLanguage
    };
  }
