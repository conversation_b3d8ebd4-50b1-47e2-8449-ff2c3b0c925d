'use client';

/**
 * ------------------------------------------------------------------
 *  Project Codex – Medical Translation Assistant (Chat UI)
 *  ---------------------------------------------------------------
 *  > ChatControls : simple stateless toolbar (clear / restart / quit)
 *  > ChatbotInterface
 *      > Holds all conversation state
 *      > Fetches language lists, performs fuzzy search + translation
 *      > Manages UI feedback (spinners, option lists, errors, etc.)
 *
 * ------------------------------------------------------------------
 */

import { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import ChatMessages from './ChatMessages';
import ChatInput from './ChatInput';
import { fetchFuzzyMatching, FuzzyMatchResult, translateMatch, fetchLanguages, submitManualTranslation } from '../services/CodexAPI';
import { DEFAULT_STARTING_LANGUAGE } from '@/lib/utils';
import { getUIString, useTranslation } from '@/lib/lang';

/** Props for <ChatControls/> button bar */

interface ChatControlsProps {
  onClearChat: () => void;
  onRestartChat: () => void;
  onQuitChat: () => void;
  isQuitted: boolean;
  onStartNewChat: () => void;
}

/** Props for <LanguageSelector/> dropdown */

interface LanguageSelectorProps {
  currentLanguage: string;
  onLanguageChange: (language: string) => void;
}

function LanguageSelector({ currentLanguage, onLanguageChange }: LanguageSelectorProps) {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  const languages = [
    { code: 'en', name: 'English', flag: '🇺🇸' },
    { code: 'uk', name: 'Українська', flag: '🇺🇦' },
    { code: 'ru', name: 'Русский', flag: '🇷🇺' }
  ];

  const currentLang = languages.find(lang => lang.code === currentLanguage) || languages[0];

  // Close dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    }

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return (
    <div className="relative" ref={dropdownRef}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center space-x-2 px-3 py-1.5 rounded-full text-xs font-medium bg-white text-indigo-600 border border-indigo-200 hover:bg-indigo-50 shadow-sm transition-colors"
        title="Change interface language"
      >
        <span className="text-sm">{currentLang.flag}</span>
        <span>{currentLang.name}</span>
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className={`h-3 w-3 transition-transform ${isOpen ? 'rotate-180' : ''}`}
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
        </svg>
      </button>

      {isOpen && (
        <div className="absolute top-full right-0 mt-1 bg-white border border-indigo-200 rounded-lg shadow-lg z-50 min-w-[140px]">
          {languages.map((language) => (
            <button
              key={language.code}
              onClick={() => {
                onLanguageChange(language.code);
                setIsOpen(false);
              }}
              className={`w-full flex items-center space-x-2 px-3 py-2 text-xs hover:bg-indigo-50 transition-colors first:rounded-t-lg last:rounded-b-lg ${
                currentLanguage === language.code ? 'bg-indigo-100 text-indigo-700' : 'text-gray-700'
              }`}
            >
              <span className="text-sm">{language.flag}</span>
              <span>{language.name}</span>
              {currentLanguage === language.code && (
                <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 ml-auto text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              )}
            </button>
          ))}
        </div>
      )}
    </div>
  );
}

function ChatControls({ onClearChat, onRestartChat, onQuitChat, isQuitted, onStartNewChat }: ChatControlsProps) {
  return (
    <div className="flex items-center justify-end space-x-2">
      {!isQuitted ? (
        <>
          <button
            onClick={onClearChat}
            className="px-3 py-1.5 rounded-full text-xs font-medium bg-white text-indigo-600 border border-indigo-200 hover:bg-indigo-50 shadow-sm transition-colors flex items-center"
            title="Clear all messages"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
            </svg>
            Clear
          </button>
          <button
            onClick={onRestartChat}
            className="px-3 py-1.5 rounded-full text-xs font-medium bg-white text-indigo-600 border border-indigo-200 hover:bg-indigo-50 shadow-sm transition-colors flex items-center"
            title="Restart conversation"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
            Restart
          </button>
          <button
            onClick={onQuitChat}
            className="px-3 py-1.5 rounded-full text-xs font-medium bg-gradient-to-r from-rose-500 to-rose-600 text-white hover:from-rose-600 hover:to-rose-700 shadow-sm transition-colors flex items-center"
            title="End conversation"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
            </svg>
            Quit
          </button>
        </>
      ) : (
        <button
          onClick={onStartNewChat}
          className="px-4 py-2 rounded-full text-sm font-medium bg-gradient-to-r from-emerald-500 to-emerald-600 text-white hover:from-emerald-600 hover:to-emerald-700 shadow-sm transition-colors flex items-center"
          title="Start new conversation"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
          </svg>
          Start New Chat
        </button>
      )}
    </div>
  );
}

interface Message {
  content: string;
  isUser: boolean;
}

/** State machine used for interactions */

type ConversationState = 'ask_source' | 'ask_target' | 'ask_word' | 'waiting_for_option';

interface LanguageItem {
  source_language: string;
  target_languages: string[];
}

const TOP_MATCH_LIMIT = 5; // Max # of fuzzy-match options shown to user

/**
 * ------------------------------------------------------------------
 *  Main component – ChatbotInterface
 * ------------------------------------------------------------------
 *  The component owns:
 *    > UI messages
 *    > Loading / restart / quit flags
 *    > Selected source & target languages
 *    > Conversation FSM state
 *    > List of fuzzy-match results for user selection
 * ------------------------------------------------------------------
 */

export default function ChatbotInterface() {
  const [messages, setMessages] = useState<Message[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [conversationState, setConversationState] = useState<ConversationState>('ask_source');
  const [sourceLanguage, setSourceLanguage] = useState<string>(DEFAULT_STARTING_LANGUAGE);
  const [targetLanguage, setTargetLanguage] = useState<string>('');
  const [fuzzyResults, setFuzzyResults] = useState<FuzzyMatchResult[]>([]);
  const [languageOptions, setLanguageOptions] = useState<LanguageItem[]>([]);
  const [isLoadingLanguages, setIsLoadingLanguages] = useState(true);
  const [isQuitted, setIsQuitted] = useState(false);
  const [isRestarting, setIsRestarting] = useState(false);
  const [skipInit, setSkipInit] = useState(false);

  const { t, changeLanguage, getCurrentLanguage } = useTranslation();
  // Use the translation hook's current language directly instead of separate state
  const interfaceLanguage = getCurrentLanguage();

  /** Mapping for “nice” language names shown to the user */

  const LANGUAGE_DISPLAY = useMemo((): Record<string, string> => ({
    'en': 'English',
    'ru': 'Russian',
    'uk': 'Ukrainian',
  }), []);

  const capitalize = useCallback((word: string) => {
    return word.charAt(0).toUpperCase() + word.slice(1);
  }, []);

  const getLanguageDisplay = useCallback((code: string): string => {
    return LANGUAGE_DISPLAY[code] || code;
  }, [LANGUAGE_DISPLAY]);

  /**
   * Deduplicate fuzzy-match results by `matching_source`
   * (keeps first occurrence only).
   */

  function checkDuplicates(results: FuzzyMatchResult[]): FuzzyMatchResult[] {
    const uniqueSourceList: string[] = [];
    const uniqueResults: FuzzyMatchResult[] = [];
    results.forEach((result) => {
      if (!(uniqueSourceList.includes(result.matching_source))) {
        uniqueResults.push(result);
        uniqueSourceList.push(result.matching_source);
      }
    });
    return uniqueResults;
  }

   /* ----------------------------------------------------------------
   *  Effects – fetch available languages on start
   * ----------------------------------------------------------------*/

  useEffect(() => {
    async function loadLanguages() {
      try {
        setIsLoadingLanguages(true);

        // Try to fetch from backend first
        try {
          const response = await fetchLanguages();

          if (response && response.available_languages) {
            const langArray: LanguageItem[] = [];
            Object.entries(response.available_languages).forEach(([index, data]) => {
              const item = data as { source_language?: string, target_languages?: string[] };
              if (item && typeof item === 'object') {
                langArray.push({
                  source_language: item.source_language || index,
                  target_languages: Array.isArray(item.target_languages) ? item.target_languages : []
                });
              }
            });

            console.log('Loaded languages from backend:', langArray);
            setLanguageOptions(langArray);
            return; // Success, exit early
          }
        } catch (backendError) {
          console.warn('Backend not available, using fallback languages:', backendError);
        }

        // Fallback to hardcoded languages for testing
        console.log('Using fallback language configuration');
        setLanguageOptions([
          { source_language: 'en', target_languages: ['ru', 'uk'] },
          { source_language: 'ru', target_languages: ['en', 'uk'] },
          { source_language: 'uk', target_languages: ['en', 'ru'] }
        ]);

      } catch (error) {
        console.error('Error in loadLanguages:', error);
        // Final fallback
        setLanguageOptions([
          { source_language: 'en', target_languages: ['ru', 'uk'] },
          { source_language: 'ru', target_languages: ['en', 'uk'] },
          { source_language: 'uk', target_languages: ['en', 'ru'] }
        ]);
      } finally {
        setIsLoadingLanguages(false);
      }
    }

    loadLanguages();
  }, []);

  const handleSourceLanguageChange = useCallback((lang: string) => {
    setSourceLanguage(lang);
    changeLanguage(lang);
  }, [changeLanguage]);

    /* ----------------------------------------------------------------
   *  Effects – initialise greeting once languages have loaded
   * ----------------------------------------------------------------*/

  useEffect(() => {
    if (messages.length === 0 && !isLoadingLanguages && languageOptions.length > 0 && !skipInit) {
      const sourceDisplayName = getLanguageDisplay(DEFAULT_STARTING_LANGUAGE);
      handleSourceLanguageChange(DEFAULT_STARTING_LANGUAGE);

      const availableSourceLanguage = languageOptions.find(
        item => item.source_language === sourceDisplayName
      );

      const targetLanguagesText = availableSourceLanguage?.target_languages
        ?.map(code => getLanguageDisplay(code))
        .join(', ') || '';

      setMessages([{
        content: `${t('startup_message')}\n\n${t('target_prompt')}\n\n${t('available_options')}: ${targetLanguagesText}.`,
        isUser: false
      }]);
      setConversationState('ask_target');
    } else if (skipInit && messages.length === 0) {
      setSkipInit(false);
    }
  }, [messages.length, languageOptions, isLoadingLanguages, skipInit, getLanguageDisplay, handleSourceLanguageChange, t]);

  const handleInterfaceLanguageChange = (lang: string) => {
    console.log('Interface language change requested:', lang);
    changeLanguage(lang);
  };

  // No need to initialize interface language - it comes directly from the translation hook

  const getTargetLanguages = (sourceLang: string): string[] => {
    const langItem = languageOptions.find(item => item.source_language === sourceLang);
    return langItem ? langItem.target_languages : [];
  };

  /* ----------------------------------------------------------------
   *  Scrolling helper – keep view pinned to latest message
   * ----------------------------------------------------------------*/

  const chatContainerRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    if (chatContainerRef.current) {
      const scrollContainer = chatContainerRef.current;
      const height = scrollContainer.scrollHeight;
      scrollContainer.scrollTo({ top: height, behavior: 'smooth' });
    }
  };

  /* =================================================================
   *  Core Input Handler – runs every time user hits “Send”
   * =================================================================
   *  Implements a very small FSM. Each state delegates to its own
   *  switch-case branch, eventually pushing a bot reply
   * ----------------------------------------------------------------*/

  const handleSendMessage = async (text: string) => {
    const trimmed_input = text.trim().toLowerCase();
    if (!trimmed_input) return;

    const userMessage = { content: trimmed_input, isUser: true };
    setMessages((prev) => [...prev, userMessage]);
    
    setTimeout(scrollToBottom, 100);
    
    setIsLoading(true);

    try {
      let botResponse = '';
      
      switch (conversationState) {
        case 'ask_source': {
          // Find the language code where the display name matches the input
          const sourceLangCodeEntry = Object.entries(LANGUAGE_DISPLAY)
            .find(entry => entry[1].toLowerCase() === trimmed_input);
          const sourceLangCode = sourceLangCodeEntry ? sourceLangCodeEntry[0] : undefined;
          
          const availableSourceLanguage = languageOptions.find(
            item => item.source_language === sourceLangCode
          );
          
          if (sourceLangCode && availableSourceLanguage) {
            handleSourceLanguageChange(sourceLangCode);

            const targetLanguagesText = availableSourceLanguage.target_languages
              .map(code => getLanguageDisplay(code))
              .join(', ');
            const targetDisplayName = getLanguageDisplay(targetLanguage);
            if ((targetLanguage === '') || (sourceLangCode === targetLanguage) || (!targetLanguagesText.includes(targetDisplayName))) {
              setConversationState('ask_target');  
              setTargetLanguage("");
              botResponse = `Great! What is your target language?\n\nAvailable options: ${targetLanguagesText}`;
            } else {
              setConversationState('ask_word');
              const sourceDisplayName = getLanguageDisplay(sourceLangCode);
              const targetDisplayName = getLanguageDisplay(targetLanguage);
              
              botResponse = `What medical term would you like to look up from ${capitalize(sourceDisplayName)} to ${capitalize(targetDisplayName)}?\n\nIf you would like to change the source or target language, type "/source" or "/target".`;
            }
          } else {
            const availableOptions = languageOptions
              .map(item => getLanguageDisplay(item.source_language))
              .join(', ');
              
            botResponse = `I didn't find that language.\n\nPlease choose from: ${availableOptions}`;
          }
          break;
        }

        case 'ask_target': {
          // Find the language code where the display name matches the input
          const targetLangCodeEntry = Object.entries(LANGUAGE_DISPLAY)
            .find(entry => entry[1].toLowerCase() === trimmed_input);
          const targetLangCode = targetLangCodeEntry ? targetLangCodeEntry[0] : undefined;
          
          const availableTargets = getTargetLanguages(sourceLanguage);
          
          if (targetLangCode && availableTargets.includes(targetLangCode)) {
            setTargetLanguage(targetLangCode);
            setConversationState('ask_word');
            
            const sourceDisplayName = getLanguageDisplay(sourceLanguage);
            const targetDisplayName = getLanguageDisplay(targetLangCode);
            
            botResponse = `What medical term would you like to look up from ${capitalize(sourceDisplayName)} to ${capitalize(targetDisplayName)}?\n\nIf you would like to change the source or target language, type "/source" or "/target".`;
          } else {
            const targetLanguagesText = availableTargets
              .map(code => getLanguageDisplay(code))
              .join(', ');
              
            botResponse = `I didn't find that language or it's not available for ${getLanguageDisplay(sourceLanguage)}.\n\nPlease choose from: ${targetLanguagesText}`;
          }
          break;
        }

        case 'ask_word':
          try {
            if (trimmed_input.includes('/source')) {
              setConversationState('ask_source');
              const targetLanguagesText = languageOptions
                .map(item => getLanguageDisplay(item.source_language))
                .join(', ');
              
              botResponse = `What is your source language?\n\nAvailable languages: ${targetLanguagesText}`;
              break;
            }
            if (trimmed_input.includes('/target')) {
              setConversationState('ask_target');
              const availableTargets = getTargetLanguages(sourceLanguage);
              const targetLanguagesText = availableTargets
                .map(code => getLanguageDisplay(code))
                .join(', ');
              
              botResponse = `What is your target language?\n\nAvailable options: ${targetLanguagesText}`;
              break;
            }
            let results: FuzzyMatchResult[] = [];

            try {
              results = await fetchFuzzyMatching({
                source_language: sourceLanguage,
                target_language: targetLanguage,
                query: trimmed_input,
                max_results: TOP_MATCH_LIMIT
              });
            } catch (backendError) {
              console.warn('Backend not available for fuzzy matching, using mock data');
              // Mock some results for testing
              if (trimmed_input.toLowerCase().includes('heart')) {
                results = [
                  { matching_name: 'Heart', matching_source: 'MOCK', matching_algorithm: 'test', matching_uid: 1, matching_row_number: 1 },
                  { matching_name: 'Cardiac', matching_source: 'MOCK', matching_algorithm: 'test', matching_uid: 2, matching_row_number: 2 }
                ];
              } else if (trimmed_input.toLowerCase().includes('pain')) {
                results = [
                  { matching_name: 'Pain', matching_source: 'MOCK', matching_algorithm: 'test', matching_uid: 3, matching_row_number: 3 },
                  { matching_name: 'Ache', matching_source: 'MOCK', matching_algorithm: 'test', matching_uid: 4, matching_row_number: 4 }
                ];
              } else {
                // Return empty to trigger AI translation
                results = [];
              }
            }
        
            if (results.length === 0) {
              // Show loading message
              setMessages((prev) => [...prev, { content: `No matches found for "${trimmed_input}". Using AI translation...`, isUser: false }]);
              
              try {
                // Call the Gemini API through the chat endpoint
                const response = await fetch('/api/chat', {
                  method: 'POST',
                  headers: {
                    'Content-Type': 'application/json',
                  },
                  body: JSON.stringify({
                    messages: [
                      { content: `Translate the medical term "${trimmed_input}" from ${getLanguageDisplay(sourceLanguage)} to ${getLanguageDisplay(targetLanguage)}. Only provide the translation without any additional text or explanation.` }
                    ]
                  }),
                });
                
                if (!response.ok) {
                  throw new Error('Failed to translate using AI');
                }
                
                const data = await response.json();
                botResponse = `AI Proposed The Translation: "${data.response}"`;
                
                // Also submit to manual translation endpoint in the background
                // This is just for logging purposes, not for showing to the user
                try {
                  await submitManualTranslation({
                    term: trimmed_input,
                    proposed_translation: data.response, // Empty as we don't have a proposed translation
                    source_language: sourceLanguage,
                    target_language: targetLanguage,
                    description: "User requested term with no fuzzy matches and this was the Gemini AI translation"
                  });
                  // No need to handle the response, this is just for logging
                  console.log(
                    `Submitted manual translation for "${trimmed_input}" (${sourceLanguage} → ${targetLanguage}) with response: "${data.response}"`
                  );
                } catch (manualError) {
                  // Just log the error, don't show to user
                  console.error('Manual translation submission error:', manualError);
                  // This won't affect the user experience
                }
              } catch (aiError) {
                console.error('AI translation error:', aiError);
                botResponse = `Sorry, I couldn't find any matches or generate a translation for "${trimmed_input}".`;
              }
              
              // Keep conversation going regardless of success/failure
              const sourceDisplayName = getLanguageDisplay(sourceLanguage);
              const targetDisplayName = getLanguageDisplay(targetLanguage);
              botResponse += `\n\nWhat else would you like to translate from ${capitalize(sourceDisplayName)} to ${capitalize(targetDisplayName)}?`;
              setConversationState('ask_word');
            } else {
              const uniqueResults = checkDuplicates(results);
              if (uniqueResults.length === 1) {
                botResponse = `Found 1 match. If this match is not close or desired, please select the last option.`;
              } else {
                botResponse = `Top ${uniqueResults.length} matches found.\n\nPlease select the closest option, or select the last option if a close match was not found.`;
              }
              const noMatch: FuzzyMatchResult = { matching_name: trimmed_input, matching_source: 'CMNF', matching_algorithm: '', matching_uid: -1, matching_row_number: -1 };
              uniqueResults.push(noMatch);
              setFuzzyResults(uniqueResults);
              setConversationState('waiting_for_option');
            }
          } catch (err) {
            console.error('Fuzzy matching error:', err);
            botResponse = `There was an error querying fuzzy matches. Please try again!`;
          }
          break;

        case 'waiting_for_option': {
          const choice = parseInt(trimmed_input);
          const optionsCount = Math.min(fuzzyResults.length, TOP_MATCH_LIMIT);
        
          if (isNaN(choice) || choice < 1 || choice > optionsCount) {
            botResponse = `Please choose a valid option number between 1 and ${optionsCount}.`;
          } else {
            const selected = fuzzyResults[choice - 1];
            handleSelection(selected);
            return; // handleSelection will update the state
          }
          break;
        }         
      }

      setMessages((prev) => [...prev, { content: botResponse, isUser: false }]);
      
      setTimeout(scrollToBottom, 100);
    } catch (error) {
      console.error('Error processing message:', error);
      setMessages((prev) => [...prev, { content: 'Sorry, I encountered an error. Please try again.', isUser: false }]);
      
      setTimeout(scrollToBottom, 100);
    } finally {
      setIsLoading(false);
    }
  };

    /* ----------------------------------------------------------------
   *  handleSelection – triggered when user clicks on a fuzzymatch
   * ----------------------------------------------------------------*/

  const handleSelection = async (selected: FuzzyMatchResult) => {
    setFuzzyResults([]);
    if (selected.matching_source === 'CMNF') {
      // Original term is saved in matching_name for use in implementation for whatever we decide to do
      let botResponse = `TEMPORARY: Close match was not found. Use Gemini or ask for more details.`;
      const sourceDisplayName = getLanguageDisplay(sourceLanguage);
      const targetDisplayName = getLanguageDisplay(targetLanguage);
      botResponse += `\n\nWhat word would you like to translate next from ${capitalize(sourceDisplayName)} to ${capitalize(targetDisplayName)}?\n\nIf you would like to change the source or target language, type "/source" or "/target".`;
      setMessages((prev) => [...prev, { content: botResponse, isUser: false }]);
      setConversationState('ask_word');
      return;
    }
    let botResponse = `Match selected: "${selected.matching_name}" (via ${selected.matching_source}). Translating...`;
    setMessages((prev) => [...prev, { content: botResponse, isUser: false }]);
    
    setTimeout(scrollToBottom, 100);

    try {
      let translations = [];

      try {
        translations = await translateMatch({
          translation_query: selected,
          target_language: targetLanguage
        });
      } catch (backendError) {
        console.warn('Backend not available for translation, using mock translation');
        // Mock translation based on target language
        const mockTranslations: Record<string, Record<string, string>> = {
          'en': {
            'Heart': 'Heart',
            'Cardiac': 'Cardiac',
            'Pain': 'Pain',
            'Ache': 'Ache'
          },
          'ru': {
            'Heart': 'Сердце',
            'Cardiac': 'Сердечный',
            'Pain': 'Боль',
            'Ache': 'Болезненность'
          },
          'uk': {
            'Heart': 'Серце',
            'Cardiac': 'Серцевий',
            'Pain': 'Біль',
            'Ache': 'Болючість'
          }
        };

        const mockTranslation = mockTranslations[targetLanguage]?.[selected.matching_name] || `[Mock ${targetLanguage} translation of ${selected.matching_name}]`;
        translations = [{
          translated_name: mockTranslation,
          translated_source: 'MOCK_DB',
          translated_uid: 999
        }];
      }

      if (translations.length === 0) {
        botResponse = `No translations were found for "${selected.matching_name}".`;
      } else {
        const t = translations[0];
        botResponse = `Translation: "${t.translated_name}"\n\nFrom: "${t.translated_source}" (UID: ${t.translated_uid})`;
      }
    } catch (error) {
      console.error('Translation error:', error);
      botResponse = `An error occurred while trying to translate.`;
    }

    const sourceDisplayName = getLanguageDisplay(sourceLanguage);
    const targetDisplayName = getLanguageDisplay(targetLanguage);
    
    // Loop back to ask for a new word
    botResponse += `\n\nWhat word would you like to translate next from ${capitalize(sourceDisplayName)} to ${capitalize(targetDisplayName)}?\n\nIf you would like to change the source or target language, type "/source" or "/target".`;
    setMessages((prev) => [...prev, { content: botResponse, isUser: false }]);
    setConversationState('ask_word');
    
    setTimeout(scrollToBottom, 100);
  };

  const handleClearChat = () => {
    setSkipInit(true);
    if (sourceLanguage && targetLanguage) {
      setMessages([{
        content: `Ready to translate from ${capitalize(getLanguageDisplay(sourceLanguage))} to ${capitalize(getLanguageDisplay(targetLanguage))}.\n\nWhat medical term would you like to look up?`,
        isUser: false
      }]);
      setConversationState('ask_word');
    } else if (sourceLanguage) {
      const availableSourceLanguage = languageOptions.find(
        item => item.source_language === sourceLanguage
      );
      const targetLanguagesText = availableSourceLanguage?.target_languages
        ?.map(code => getLanguageDisplay(code))
        .join(', ') || '';
      
      setMessages([{
        content: `Source language: ${capitalize(getLanguageDisplay(sourceLanguage))}\n\n${getUIString('target_prompt', sourceLanguage)}\n\n${getUIString('available_options', sourceLanguage)}: ${targetLanguagesText}.`,
        isUser: false
      }]);
      setConversationState('ask_target');
    } else {
      const languageDisplays = languageOptions
        .map(item => getLanguageDisplay(item.source_language))
        .join(', ');
      
      setMessages([{
        content: `Hi! What is your source language? Available languages: ${languageDisplays}`,
        isUser: false
      }]);
      setConversationState('ask_source');
    }
    
    setFuzzyResults([]);
    setTimeout(scrollToBottom, 100);
  };

  /* ----------------------------------------------------------------
   *  Utility handlers for Clear / Restart / Quit
   * ----------------------------------------------------------------*/

  const handleRestartChat = () => {
    setIsRestarting(true);
    setSkipInit(true);
    setMessages([]);
    setTimeout(() => {
      const currentSourceLang = sourceLanguage;
      const currentTargetLang = targetLanguage;
      setFuzzyResults([]);
      setIsRestarting(false);
      if (currentSourceLang && currentTargetLang) {
        setConversationState('ask_word');
        setMessages([{
          content: `Ready to translate from ${capitalize(getLanguageDisplay(currentSourceLang))} to ${capitalize(getLanguageDisplay(currentTargetLang))}.\n\nWhat medical term would you like to look up?`,
          isUser: false
        }]);
      } else if (currentSourceLang) {
        setConversationState('ask_target');
        const availableSourceLanguage = languageOptions.find(
          item => item.source_language === currentSourceLang
        );
        const targetLanguagesText = availableSourceLanguage?.target_languages
          ?.map(code => getLanguageDisplay(code))
          .join(', ') || '';
        
        setMessages([{
          content: `${getUIString('target_prompt', currentSourceLang)}\n\n${getUIString('available_options', currentSourceLang)}: ${targetLanguagesText}.`,
          isUser: false
        }]);
      } else {
        setConversationState('ask_source');
        const languageDisplays = languageOptions
          .map(item => getLanguageDisplay(item.source_language))
          .join(', ');
        
        setMessages([{
          content: `Hi! What is your source language? Available languages: ${languageDisplays}`,
          isUser: false
        }]);
      }
      setTimeout(scrollToBottom, 100);
    }, 1500);
  };

  const handleQuitChat = () => {
    setIsQuitted(true);
    setMessages([{
      content: "Chat ended. Click 'Start New Chat' to begin a new conversation.",
      isUser: false
    }]);
    setTimeout(scrollToBottom, 100);
  };

  const handleStartNewChat = () => {
    setIsQuitted(false);
    setSkipInit(true);
    setMessages([]);
    const currentSourceLang = sourceLanguage;
    const currentTargetLang = targetLanguage;
    setFuzzyResults([]);
    if (currentSourceLang && currentTargetLang) {
      setConversationState('ask_word');
      setMessages([{
        content: `Ready to translate from ${capitalize(getLanguageDisplay(currentSourceLang))} to ${capitalize(getLanguageDisplay(currentTargetLang))}.\n\nWhat medical term would you like to look up?`,
        isUser: false
      }]);
    } else if (currentSourceLang) {
      setConversationState('ask_target');
      const availableSourceLanguage = languageOptions.find(
        item => item.source_language === currentSourceLang
      );
      const targetLanguagesText = availableSourceLanguage?.target_languages
        ?.map(code => getLanguageDisplay(code))
        .join(', ') || '';
      
      setMessages([{
        content: `${getUIString('target_prompt', currentSourceLang)}\n\n${getUIString('available_options', currentSourceLang)}: ${targetLanguagesText}.`,
        isUser: false
      }]);
    } else {
      setConversationState('ask_source');
      const languageDisplays = languageOptions
        .map(item => getLanguageDisplay(item.source_language))
        .join(', ');
      
      setMessages([{
        content: `Hi! What is your source language? Available languages: ${languageDisplays}`,
        isUser: false
      }]);
    }
    setTimeout(scrollToBottom, 100);
  };

  useEffect(() => {
    const scrollTimeout = setTimeout(() => {
      scrollToBottom();
    }, 100);
    
    return () => clearTimeout(scrollTimeout);
  }, [messages]);

  useEffect(() => {
    const chatInput = document.querySelector('input[type="text"]');
    
    if (chatInput) {
      const handleFocus = () => {
        setTimeout(scrollToBottom, 50);
      };
      
      chatInput.addEventListener('focus', handleFocus);
      
      return () => {
        chatInput.removeEventListener('focus', handleFocus);
      };
    }
  }, []);

  return (
    <div className="flex flex-col bg-gradient-to-b from-indigo-50 to-blue-50 rounded-2xl shadow-xl h-[90vh] overflow-hidden border border-indigo-100 max-w-4xl mx-auto relative">
      {isRestarting && (
        <div className="absolute inset-0 bg-white/90 z-50 flex items-center justify-center backdrop-blur-sm">
          <div className="flex flex-col items-center">
            <div className="relative">
              <div className="w-20 h-20 rounded-full bg-indigo-100 flex items-center justify-center mb-4">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10 text-indigo-600 animate-spin" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
              </div>
              <div className="absolute -top-1 -right-1 w-6 h-6 bg-indigo-500 rounded-full animate-ping"></div>
              <div className="absolute -bottom-1 -left-1 w-4 h-4 bg-indigo-300 rounded-full animate-ping" style={{ animationDelay: '300ms' }}></div>
            </div>
            <p className="text-indigo-700 font-medium text-lg">Restarting chat...</p>
            <div className="mt-2 flex space-x-1">
              <div className="w-2 h-2 bg-indigo-400 rounded-full animate-bounce" style={{ animationDelay: '0ms' }}></div>
              <div className="w-2 h-2 bg-indigo-500 rounded-full animate-bounce" style={{ animationDelay: '150ms' }}></div>
              <div className="w-2 h-2 bg-indigo-600 rounded-full animate-bounce" style={{ animationDelay: '300ms' }}></div>
            </div>
          </div>
        </div>
      )}

      <header className="p-5 border-b border-indigo-100 bg-white sticky top-0 z-10 flex items-center justify-between">
        <div className="flex items-center">
          <div className="mr-3 flex items-center justify-center bg-indigo-600 h-10 w-10 rounded-full shadow-sm">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9" />
            </svg>
          </div>
          <div>
            <h1 className="text-xl font-semibold text-indigo-900">Project Codex</h1>
            <p className="text-sm text-indigo-500 flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129" />
              </svg>
              Medical Translation Assistant
            </p>
          </div>
        </div>
        
        <div className="flex items-center space-x-3">
          {sourceLanguage && targetLanguage && (
            <div className="bg-indigo-100 px-3 py-1.5 rounded-full text-xs text-indigo-700 font-medium flex items-center shadow-sm">
              <span className="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129" />
                </svg>
                {capitalize(getLanguageDisplay(sourceLanguage))}
              </span>
              <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mx-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
              </svg>
              <span className="flex items-center">
                {capitalize(getLanguageDisplay(targetLanguage))}
              </span>
            </div>
          )}

          {/* Debug indicator */}
          <div className="text-xs text-gray-500 px-2 py-1 bg-gray-100 rounded">
            UI: {interfaceLanguage} | Hook: {getCurrentLanguage()}
          </div>

          <LanguageSelector
            currentLanguage={interfaceLanguage}
            onLanguageChange={handleInterfaceLanguageChange}
          />

          <ChatControls
            onClearChat={handleClearChat}
            onRestartChat={handleRestartChat}
            onQuitChat={handleQuitChat}
            isQuitted={isQuitted}
            onStartNewChat={handleStartNewChat}
          />
        </div>
      </header>

      {/* Matches area */}
      {fuzzyResults.length > 0 && (
        <>
            <div className="p-4 border-t border-gray-200 bg-white">
              <h2 className="text-lg font-semibold text-gray-800 mb-2">Available options:</h2>
              <ul className="space-y-2">
                {fuzzyResults.slice(0, TOP_MATCH_LIMIT).map((match, index) => (
                  <li
                    key={index}
                    className="p-3 bg-gray-50 rounded-lg hover:bg-gray-100 cursor-pointer transition-colors"
                    onClick = {() => handleSelection(match)}
                  >
                    {match.matching_source === 'CMNF' && 
                      <>
                        {index + 1}. Close Match was Not Found
                      </>}
                    {match.matching_source !== 'CMNF' &&
                      <>
                        {index + 1}. {match.matching_name} - Source: {match.matching_source}
                      </>
                    }
                  </li>
                ))}
              </ul>
            </div>
          </>
        )}

      {/* Chat area with softer background */}
      <div ref={chatContainerRef} className="flex-grow overflow-y-auto px-6 py-6 bg-gradient-to-br from-[#f5f7ff] to-[#f8faff] scroll-smooth">
        {isLoadingLanguages ? (
          <div className="flex justify-center items-center h-full">
            <div className="flex flex-col items-center space-y-3">
              <div className="flex items-center space-x-2 bg-white px-4 py-2 rounded-full shadow-sm border border-indigo-100">
                <div className="w-2 h-2 bg-indigo-400 rounded-full animate-bounce" style={{ animationDelay: '0ms' }}></div>
                <div className="w-2 h-2 bg-indigo-500 rounded-full animate-bounce" style={{ animationDelay: '150ms' }}></div>
                <div className="w-2 h-2 bg-indigo-600 rounded-full animate-bounce" style={{ animationDelay: '300ms' }}></div>
                <span className="text-sm text-indigo-600 ml-2">Loading languages...</span>
              </div>
              <p className="text-xs text-indigo-400">Connecting to Project Codex database</p>
            </div>
          </div>
        ) : (
          <>
            <ChatMessages messages={messages} />
            {isLoading && (
              <div className="flex justify-center my-4">
                <div className="flex items-center space-x-2 bg-white px-4 py-2 rounded-full shadow-sm border border-indigo-100">
                  <div className="w-2 h-2 bg-indigo-400 rounded-full animate-bounce" style={{ animationDelay: '0ms' }}></div>
                  <div className="w-2 h-2 bg-indigo-500 rounded-full animate-bounce" style={{ animationDelay: '150ms' }}></div>
                  <div className="w-2 h-2 bg-indigo-600 rounded-full animate-bounce" style={{ animationDelay: '300ms' }}></div>
                  <span className="text-sm text-indigo-600 ml-2">Translating...</span>
                </div>
              </div>
            )}
          </>
        )}
      </div>

      <footer className="p-4 border-t border-indigo-100 bg-white">
        {!isQuitted ? (
          <>
            <ChatInput onSend={handleSendMessage} isLoading={isLoading || isLoadingLanguages} />
            <div className="mt-3 p-2 rounded-lg bg-indigo-50 border border-indigo-100">
              <div className="text-xs font-medium flex items-center justify-center mb-2">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5 mr-1 text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9" />
                </svg>
                <span className="text-indigo-700">Available translations</span>
              </div>
              <div className="flex flex-wrap justify-center gap-2">
                {languageOptions.map((item, index) => (
                  <div key={index} className="flex items-center bg-white px-2 py-1 rounded-md text-xs border border-indigo-100 shadow-sm hover:shadow hover:border-indigo-200 transition-all cursor-default">
                    <span className="font-medium text-indigo-700">{getLanguageDisplay(item.source_language)}</span>
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mx-1 text-indigo-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                    </svg>
                    <span className="text-indigo-600">
                      {item.target_languages.map(code => getLanguageDisplay(code)).join(', ')}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          </>
        ) : (
          <div className="flex justify-center">
            <ChatControls 
              onClearChat={handleClearChat}
              onRestartChat={handleRestartChat}
              onQuitChat={handleQuitChat}
              isQuitted={isQuitted}
              onStartNewChat={handleStartNewChat}
            />
          </div>
        )}
      </footer>
    </div>
  );
}
